import React from 'react';

interface BottomNavigationProps {
  active?: 'Home' | 'Orders' | 'Table' | 'More';
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ active = 'Home' }) => {
  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white shadow-[0px_-2px_16px_0px_rgba(0,0,0,0.08)]">
      <div className="flex flex-row items-center relative w-full">
        <div className="flex flex-row gap-[104px] items-center justify-start px-8 py-1.5 relative w-full">
          {/* Sol grup - Home ve Orders */}
          <div className="flex flex-row gap-4 items-center justify-start h-11 flex-1">
            {/* Home Button */}
            <div className={`flex-1 h-full rounded-2xl ${active === 'Home' ? 'bg-[#f0f8ff]' : ''}`}>
              <div className="flex flex-row items-center justify-center h-full">
                <div className="flex flex-row gap-2.5 items-center justify-center px-4 py-[13px]">
                  <div className="w-6 h-6">
                    <svg className="w-full h-full" viewBox="0 0 24 24" fill={active === 'Home' ? '#025CCA' : '#636566'}>
                      <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
                    </svg>
                  </div>
                  <span className={`font-semibold text-[14px] leading-[1.5] ${active === 'Home' ? 'text-[#025cca]' : 'text-[#636566]'}`}>
                    Ana Sayfa
                  </span>
                </div>
              </div>
            </div>

            {/* Orders Button */}
            <div className={`flex-1 h-full rounded-2xl ${active === 'Orders' ? 'bg-[#f0f8ff]' : ''}`}>
              <div className="flex flex-row items-center justify-center h-full">
                <div className="flex flex-row gap-2.5 items-center justify-center p-4">
                  <div className="w-6 h-6">
                    <svg className="w-full h-full" viewBox="0 0 24 24" fill="none" stroke={active === 'Orders' ? '#025CCA' : '#636566'} strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <span className={`font-medium text-[14px] leading-[1.5] ${active === 'Orders' ? 'text-[#025cca]' : 'text-[#636566]'}`}>
                    Siparişler
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Sağ grup - Table ve More */}
          <div className="flex flex-row gap-4 items-center justify-start h-11 flex-1">
            {/* Table Button */}
            <div className={`flex-1 h-full rounded-2xl ${active === 'Table' ? 'bg-[#f0f8ff]' : ''}`}>
              <div className="flex flex-row items-center justify-center h-full">
                <div className="flex flex-row gap-2.5 items-center justify-center p-4">
                  <div className="w-6 h-6">
                    <svg className="w-full h-full" viewBox="0 0 24 24" fill="none" stroke={active === 'Table' ? '#025CCA' : '#636566'} strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                  </div>
                  <span className={`font-medium text-[14px] leading-[1.5] ${active === 'Table' ? 'text-[#025cca]' : 'text-[#636566]'}`}>
                    Masalar
                  </span>
                </div>
              </div>
            </div>

            {/* More Button */}
            <div className={`flex-1 h-full rounded-2xl ${active === 'More' ? 'bg-[#f0f8ff]' : ''}`}>
              <div className="flex flex-row items-center justify-center h-full">
                <div className="flex flex-row gap-2.5 items-center justify-center p-4">
                  <div className="w-6 h-6">
                    <svg className="w-full h-full" viewBox="0 0 24 24" fill="none" stroke={active === 'More' ? '#025CCA' : '#636566'} strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                    </svg>
                  </div>
                  <span className={`font-medium text-[14px] leading-[1.5] ${active === 'More' ? 'text-[#025cca]' : 'text-[#636566]'}`}>
                    Daha Fazla
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Ortadaki büyük buton */}
          <div className="absolute bg-[#025cca] left-1/2 rounded-[99px] w-[72px] h-[72px] top-[-28px] -translate-x-1/2">
            <div className="absolute left-1/2 w-8 h-8 top-1/2 -translate-x-1/2 -translate-y-1/2">
              <svg className="w-full h-full" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default BottomNavigation;
