// Dashboard Metric Card Component
import React from 'react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change: number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconColor: string;
  format?: 'currency' | 'number';
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  iconBgColor,
  iconColor,
  format = 'number'
}) => {
  const formatValue = (val: string | number) => {
    if (format === 'currency') {
      const numValue = typeof val === 'string' ? parseFloat(val) : val;
      return `$${numValue.toLocaleString('tr-TR', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
    return val.toString();
  };

  const getChangeColor = (changeValue: number) => {
    if (changeValue > 0) return 'text-green-600';
    if (changeValue < 0) return 'text-red-600';
    return 'text-gray-500';
  };

  const getChangeText = (changeValue: number) => {
    if (changeValue === 0) return '0% dünden';
    const prefix = changeValue > 0 ? '+' : '';
    return `${prefix}${changeValue.toFixed(1)}% dünden`;
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mb-2">
            {formatValue(value)}
          </p>
          <p className={`text-sm ${getChangeColor(change)}`}>
            {getChangeText(change)}
          </p>
        </div>
        
        <div className={`w-12 h-12 ${iconBgColor} rounded-lg flex items-center justify-center flex-shrink-0`}>
          <div className={iconColor}>
            {icon}
          </div>
        </div>
      </div>
    </div>
  );
};
