// Order Card Component
import React from 'react';
import { OrderSummary, OrderStatus } from '../../types/dashboard.types';
import { Button } from '../common/Button';

interface OrderCardProps {
  order: OrderSummary;
  showPayButton?: boolean;
  onPayClick?: (orderId: string) => void;
}

export const OrderCard: React.FC<OrderCardProps> = ({
  order,
  showPayButton = false,
  onPayClick
}) => {
  const getStatusInfo = (status: OrderStatus) => {
    switch (status) {
      case 'CONFIRMED':
        return {
          text: 'Onaylandı',
          icon: '✓',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
          dotColor: 'bg-blue-500'
        };
      case 'PREPARING':
        return {
          text: 'Hazırlanıyor',
          icon: '👨‍🍳',
          bgColor: 'bg-orange-100',
          textColor: 'text-orange-800',
          dotColor: 'bg-orange-500'
        };
      case 'READY':
        return {
          text: 'Hazır',
          icon: '✅',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          dotColor: 'bg-green-500'
        };
      case 'PENDING':
        return {
          text: 'Bekliyor',
          icon: '⏳',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
          dotColor: 'bg-yellow-500'
        };
      default:
        return {
          text: 'Bilinmiyor',
          icon: '❓',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          dotColor: 'bg-gray-500'
        };
    }
  };

  const statusInfo = getStatusInfo(order.status);

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCustomerDisplayName = () => {
    if (order.customerName) {
      return order.customerName;
    }
    if (order.tableNumber) {
      return `Masa ${order.tableNumber}`;
    }
    return 'Müşteri';
  };

  const getOrderIdDisplay = () => {
    return order.orderNumber || order.id.slice(-6).toUpperCase();
  };

  return (
    <div className="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          {/* Order ID Badge */}
          <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">
            {getOrderIdDisplay()}
          </div>
          
          {/* Status */}
          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.textColor}`}>
            <div className={`w-2 h-2 ${statusInfo.dotColor} rounded-full mr-1`}></div>
            {statusInfo.text}
          </div>
        </div>

        {/* Pay Button */}
        {showPayButton && onPayClick && (
          <Button
            onClick={() => onPayClick(order.id)}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Ödeme Al
          </Button>
        )}
      </div>

      {/* Customer Info */}
      <div className="mb-3">
        <h3 className="font-medium text-gray-900 text-sm">
          {getCustomerDisplayName()}
        </h3>
        <p className="text-xs text-gray-500">
          {order.itemCount} ürün • {formatTime(order.orderedAt)}
        </p>
      </div>

      {/* Order Items Preview */}
      <div className="mb-3">
        <div className="text-xs text-gray-600 space-y-1">
          {order.items.slice(0, 2).map((item, index) => (
            <div key={item.id} className="flex justify-between">
              <span className="truncate">
                {item.quantity}x {item.productName}
              </span>
              <span className="font-medium">
                ₺{item.totalPrice.toLocaleString('tr-TR')}
              </span>
            </div>
          ))}
          {order.items.length > 2 && (
            <div className="text-gray-400 text-center">
              +{order.items.length - 2} ürün daha...
            </div>
          )}
        </div>
      </div>

      {/* Total */}
      <div className="flex justify-between items-center pt-2 border-t border-gray-100">
        <span className="text-sm font-medium text-gray-600">Toplam:</span>
        <span className="text-sm font-bold text-gray-900">
          ₺{order.totalAmount.toLocaleString('tr-TR')}
        </span>
      </div>
    </div>
  );
};
