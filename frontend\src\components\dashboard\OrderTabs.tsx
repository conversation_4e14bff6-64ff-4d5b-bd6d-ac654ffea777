// Order Tabs Component
import React from 'react';
import { DashboardTab } from '../../types/dashboard.types';

interface OrderTabsProps {
  activeTab: DashboardTab;
  onTabChange: (tab: DashboardTab) => void;
  inProgressCount: number;
  waitingPaymentCount: number;
}

export const OrderTabs: React.FC<OrderTabsProps> = ({
  activeTab,
  onTabChange,
  inProgressCount,
  waitingPaymentCount
}) => {
  const tabs = [
    {
      id: 'in-progress' as DashboardTab,
      label: 'İşlemde',
      count: inProgressCount
    },
    {
      id: 'waiting-payment' as DashboardTab,
      label: 'Ödeme Bekliyor',
      count: waitingPaymentCount
    }
  ];

  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`
              py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap
              ${activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            {tab.label}
            {tab.count > 0 && (
              <span className={`
                ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full
                ${activeTab === tab.id
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-gray-100 text-gray-600'
                }
              `}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </nav>
    </div>
  );
};
