// Out of Stock Product Item Component
import React from 'react';
import { OutOfStockProduct } from '../../types/dashboard.types';

interface OutOfStockItemProps {
  product: OutOfStockProduct;
}

export const OutOfStockItem: React.FC<OutOfStockItemProps> = ({ product }) => {
  return (
    <div className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      {/* Product Image */}
      <div className="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg overflow-hidden relative">
        {product.image ? (
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover opacity-60"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center opacity-60">
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}
        
        {/* Out of stock overlay */}
        <div className="absolute inset-0 bg-red-500 bg-opacity-20 flex items-center justify-center">
          <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 truncate">
          {product.name}
        </h4>
        <p className="text-xs text-gray-500 truncate">
          {product.categoryName}
        </p>
        {product.estimatedRestockTime && (
          <p className="text-xs text-orange-600 mt-1">
            Tahmini: {product.estimatedRestockTime}
          </p>
        )}
      </div>

      {/* Status Badge */}
      <div className="flex-shrink-0">
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Stok Yok
        </span>
      </div>
    </div>
  );
};
