// Popular Product Item Component
import React from 'react';
import { PopularProduct } from '../../types/dashboard.types';

interface PopularProductItemProps {
  product: PopularProduct;
  rank: number;
}

export const PopularProductItem: React.FC<PopularProductItemProps> = ({
  product,
  rank
}) => {
  const formatRank = (rankNumber: number) => {
    return rankNumber.toString().padStart(2, '0');
  };

  return (
    <div className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      {/* Rank */}
      <div className="flex-shrink-0 w-8 text-center">
        <span className="text-sm font-semibold text-gray-500">
          {formatRank(rank)}
        </span>
      </div>

      {/* Product Image */}
      <div className="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
        {product.image ? (
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 truncate">
          {product.name}
        </h4>
        <p className="text-xs text-gray-500 truncate">
          {product.categoryName}
        </p>
        <p className="text-xs text-gray-600 mt-1">
          Sipariş: <span className="font-medium">{product.totalOrders}</span>
        </p>
      </div>
    </div>
  );
};
