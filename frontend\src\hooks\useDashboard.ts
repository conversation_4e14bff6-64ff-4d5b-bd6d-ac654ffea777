// Dashboard hook
import { useEffect, useCallback } from 'react';
import { useDashboardStore } from '../store/dashboardStore';
import { apiService } from '../services/api.service';
import { DashboardTab } from '../types/dashboard.types';
import toast from 'react-hot-toast';

export const useDashboard = () => {
  const {
    dashboardData,
    uiState,
    setDashboardData,
    setActiveTab,
    setSearchQuery,
    setLoading,
    setError,
    setLastRefresh,
    getFilteredOrders,
    getMetrics,
    getPopularProducts,
    getOutOfStockProducts,
    reset,
  } = useDashboardStore();

  // Dashboard verilerini yükle
  const fetchDashboardData = useCallback(async (showToast = false) => {
    setLoading(true);
    setError(null);

    try {
      const data = await apiService.getDashboardData();
      setDashboardData(data);
      
      if (showToast) {
        toast.success('Dashboard verileri güncellendi');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Dashboard verileri yüklenemedi';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Dashboard fetch error:', error);
    } finally {
      setLoading(false);
    }
  }, [setDashboardData, setLoading, setError]);

  // Sadece metrikleri yenile (daha hızlı)
  const refreshMetrics = useCallback(async () => {
    try {
      const metrics = await apiService.getDashboardMetrics();
      
      if (dashboardData) {
        setDashboardData({
          ...dashboardData,
          metrics,
          lastUpdated: new Date(),
        });
      }
    } catch (error: any) {
      console.error('Metrics refresh error:', error);
      // Metrik yenileme hatası için toast gösterme, sessizce başarısız ol
    }
  }, [dashboardData, setDashboardData]);

  // Tab değiştir
  const changeTab = useCallback((tab: DashboardTab) => {
    setActiveTab(tab);
  }, [setActiveTab]);

  // Arama yap
  const search = useCallback((query: string) => {
    setSearchQuery(query);
  }, [setSearchQuery]);

  // Manuel yenileme
  const refresh = useCallback(() => {
    fetchDashboardData(true);
  }, [fetchDashboardData]);

  // Sayfa yüklendiğinde verileri getir
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Otomatik yenileme (her 30 saniyede bir sadece metrikler)
  useEffect(() => {
    const interval = setInterval(() => {
      if (dashboardData && !uiState.isLoading) {
        refreshMetrics();
      }
    }, 30000); // 30 saniye

    return () => clearInterval(interval);
  }, [dashboardData, uiState.isLoading, refreshMetrics]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Component unmount olduğunda store'u temizleme
      // reset();
    };
  }, []);

  return {
    // Data
    dashboardData,
    metrics: getMetrics(),
    popularProducts: getPopularProducts(),
    outOfStockProducts: getOutOfStockProducts(),
    filteredOrders: getFilteredOrders(),
    
    // UI State
    activeTab: uiState.activeTab,
    searchQuery: uiState.searchQuery,
    isLoading: uiState.isLoading,
    error: uiState.error,
    lastRefresh: uiState.lastRefresh,
    
    // Actions
    changeTab,
    search,
    refresh,
    fetchDashboardData,
    refreshMetrics,
    reset,
    
    // Computed
    hasData: !!dashboardData,
    isEmpty: dashboardData ? (
      dashboardData.ordersInProgress.length === 0 && 
      dashboardData.ordersWaitingPayment.length === 0
    ) : false,
    ordersInProgressCount: dashboardData?.ordersInProgress.length || 0,
    ordersWaitingPaymentCount: dashboardData?.ordersWaitingPayment.length || 0,
  };
};
