// Dashboard page - Ana panel
import React from 'react';
import { useAuth } from '../hooks/useAuth';
import { useDashboard } from '../hooks/useDashboard';
// import { Button } from '../components/common/Button'; // Will be used for logout later
import { MetricCard } from '../components/dashboard/MetricCard';
import { PopularProductItem } from '../components/dashboard/PopularProductItem';
import { OutOfStockItem } from '../components/dashboard/OutOfStockItem';
import { OrderCard } from '../components/dashboard/OrderCard';
import { OrderTabs } from '../components/dashboard/OrderTabs';
import { EmptyState } from '../components/dashboard/EmptyState';

export const DashboardPage: React.FC = () => {
  const { employee } = useAuth();
  // const { logout, isLoading: authLoading } = useAuth(); // Will be used for logout later
  const {
    metrics,
    popularProducts,
    outOfStockProducts,
    filteredOrders,
    activeTab,
    searchQuery,
    isLoading,
    error,
    hasData,
    isEmpty,
    ordersInProgressCount,
    ordersWaitingPaymentCount,
    changeTab,
    search,
    refresh
  } = useDashboard();

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      BRANCH_MANAGER: 'Şube Müdürü',
      CASHIER: 'Kasiyer',
      WAITER: 'Garson',
      KITCHEN: 'Mutfak',
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  const handlePayClick = (orderId: string) => {
    // TODO: Navigate to payment page
    console.log('Pay order:', orderId);
  };

  const handleCreateOrder = () => {
    // TODO: Navigate to create order page
    console.log('Create new order');
  };

  const getCurrentTime = () => {
    return new Date().toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <div className="h-screen w-screen bg-gray-100 flex flex-col overflow-hidden">
      {/* Header */}
      <header className="bg-white shadow-sm border-b flex-shrink-0">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">Atorpos POS</h1>
              <span className="text-sm text-gray-500">Ana Şube</span>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-lg mx-8">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Arama yapın..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="relative p-2 text-gray-400 hover:text-gray-500">
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400"></span>
              </button>

              {employee && (
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {employee.firstName.charAt(0)}{employee.lastName.charAt(0)}
                    </span>
                  </div>
                  <div className="text-sm">
                    <div className="font-medium text-gray-900">
                      {employee.firstName} {employee.lastName}
                    </div>
                    <div className="text-gray-500">
                      {getRoleDisplayName(employee.role)}
                    </div>
                  </div>
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 overflow-hidden">
        <div className="h-full px-4 py-6 sm:px-6 lg:px-8 overflow-y-auto pb-20 lg:pb-6">
          {/* Welcome Section */}
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-1">
                Günaydın, {employee?.firstName}
              </h2>
              <p className="text-gray-600 flex items-center">
                Müşterilerinize en iyi hizmeti verin 😊
              </p>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && !hasData && (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Dashboard yükleniyor...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                  <button
                    onClick={refresh}
                    className="mt-2 text-sm text-red-600 hover:text-red-500 underline"
                  >
                    Tekrar dene
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Dashboard Content */}
          {hasData && (
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              {/* Left Column - Metrics and Lists */}
              <div className="xl:col-span-2 space-y-6">
                {/* Time Display - Sol üstte */}
                <div className="flex justify-end mb-4">
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {getCurrentTime()}
                    </div>
                    <div className="text-sm text-gray-500">
                      {getCurrentDate()}
                    </div>
                  </div>
                </div>
                {/* Metrics Cards */}
                {metrics && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <MetricCard
                      title="Günlük Kazanç"
                      value={metrics.dailyRevenue.amount}
                      change={metrics.dailyRevenue.changeFromYesterday}
                      format="currency"
                      icon={
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      }
                      iconBgColor="bg-green-100"
                      iconColor="text-green-600"
                    />

                    <MetricCard
                      title="İşlemde"
                      value={metrics.ordersInProgress.count}
                      change={metrics.ordersInProgress.changeFromYesterday}
                      icon={
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      }
                      iconBgColor="bg-orange-100"
                      iconColor="text-orange-600"
                    />

                    <MetricCard
                      title="Ödeme Bekliyor"
                      value={metrics.ordersWaitingPayment.count}
                      change={metrics.ordersWaitingPayment.changeFromYesterday}
                      icon={
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      }
                      iconBgColor="bg-blue-100"
                      iconColor="text-blue-600"
                    />
                  </div>
                )}

                {/* Popular Dishes and Out of Stock */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Popular Dishes */}
                  <div className="bg-white rounded-lg shadow-sm border">
                    <div className="p-4 border-b border-gray-200">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-medium text-gray-900">Popüler Ürünler</h3>
                        <button className="text-sm text-blue-600 hover:text-blue-500">
                          Tümünü Gör
                        </button>
                      </div>
                    </div>
                    <div className="p-4">
                      {popularProducts.length > 0 ? (
                        <div className="space-y-2">
                          {popularProducts.slice(0, 5).map((product, index) => (
                            <PopularProductItem
                              key={product.id}
                              product={product}
                              rank={index + 1}
                            />
                          ))}
                        </div>
                      ) : (
                        <p className="text-center text-gray-500 py-4">
                          Bugün henüz satılan ürün yok.
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Out of Stock */}
                  <div className="bg-white rounded-lg shadow-sm border">
                    <div className="p-4 border-b border-gray-200">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-medium text-gray-900">Stok Dışı</h3>
                        <button className="text-sm text-blue-600 hover:text-blue-500">
                          Tümünü Gör
                        </button>
                      </div>
                    </div>
                    <div className="p-4">
                      {outOfStockProducts.length > 0 ? (
                        <div className="space-y-2">
                          {outOfStockProducts.map((product) => (
                            <OutOfStockItem
                              key={product.id}
                              product={product}
                            />
                          ))}
                        </div>
                      ) : (
                        <p className="text-center text-gray-500 py-4">
                          Tüm stok kalemleri hazır!
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Orders */}
              <div className="xl:col-span-1">
                <div className="bg-white rounded-lg shadow-sm border">
                  {/* Order Tabs */}
                  <div className="p-4 border-b border-gray-200">
                    <OrderTabs
                      activeTab={activeTab}
                      onTabChange={changeTab}
                      inProgressCount={ordersInProgressCount}
                      waitingPaymentCount={ordersWaitingPaymentCount}
                    />
                  </div>

                  {/* Search */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        placeholder="Sipariş ara..."
                        value={searchQuery}
                        onChange={(e) => search(e.target.value)}
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>

                  {/* Orders List */}
                  <div className="p-4">
                    {filteredOrders.length > 0 ? (
                      <div className="space-y-4 max-h-[calc(100vh-400px)] overflow-y-auto">
                        {filteredOrders.map((order) => (
                          <OrderCard
                            key={order.id}
                            order={order}
                            showPayButton={activeTab === 'waiting-payment'}
                            onPayClick={handlePayClick}
                          />
                        ))}
                      </div>
                    ) : isEmpty ? (
                      <EmptyState
                        title="Henüz Sipariş Yok"
                        description="Bugün müşterilerinizden henüz sipariş almadınız."
                        actionLabel="Yeni Sipariş Oluştur"
                        onAction={handleCreateOrder}
                        icon={
                          <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        }
                      />
                    ) : (
                      <div className="text-center py-8">
                        <svg className="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <p className="text-gray-500">
                          {searchQuery ? `"${searchQuery}" için sonuç bulunamadı` : 'Sipariş bulunamadı'}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Bottom Navigation - Only on mobile */}
      <nav className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 flex-shrink-0">
        <div className="flex justify-around items-center max-w-md mx-auto">
          <button className="flex flex-col items-center py-2 px-3 text-blue-600">
            <svg className="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </svg>
            <span className="text-xs font-medium">Ana Sayfa</span>
          </button>

          <button className="flex flex-col items-center py-2 px-3 text-gray-400">
            <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span className="text-xs">Siparişler</span>
          </button>

          <button className="flex flex-col items-center justify-center w-12 h-12 bg-blue-600 rounded-full text-white">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>

          <button className="flex flex-col items-center py-2 px-3 text-gray-400">
            <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            <span className="text-xs">Masalar</span>
          </button>

          <button className="flex flex-col items-center py-2 px-3 text-gray-400">
            <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
            </svg>
            <span className="text-xs">Diğer</span>
          </button>
        </div>
      </nav>
    </div>
  );
};
