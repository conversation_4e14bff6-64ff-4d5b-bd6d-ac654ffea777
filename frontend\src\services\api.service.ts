// API service - Axios ile backend iletişimi
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { Employee, LoginRequest, LoginResponse } from '../types/auth.types';
import { DashboardData, DashboardMetrics, PopularProduct, OutOfStockProduct, OrderSummary } from '../types/dashboard.types';
import { useAuthStore } from '../store/authStore';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api/v1',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor - Token ekleme
    this.api.interceptors.request.use(
      (config) => {
        const token = useAuthStore.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor - Error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token geçersiz, logout yap
          useAuthStore.getState().logout();
        }
        return Promise.reject(error);
      }
    );
  }

  // Aktif çalışanları getir
  async getActiveEmployees(): Promise<Employee[]> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        data: Employee[];
        count: number;
      }> = await this.api.get('/employees/active-shift');
      
      return response.data.data;
    } catch (error) {
      console.error('Aktif çalışanlar getirilemedi:', error);
      throw error;
    }
  }

  // PIN ile giriş
  async pinLogin(loginData: LoginRequest): Promise<LoginResponse> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        message: string;
        data: LoginResponse;
      }> = await this.api.post('/auth/pin-login', loginData);
      
      return response.data.data;
    } catch (error) {
      console.error('PIN giriş hatası:', error);
      throw error;
    }
  }

  // Token doğrulama
  async verifyToken(): Promise<boolean> {
    try {
      await this.api.get('/auth/verify');
      return true;
    } catch (error) {
      return false;
    }
  }

  // Kullanıcı profili
  async getProfile(): Promise<LoginResponse['employee']> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        data: LoginResponse['employee'];
      }> = await this.api.get('/auth/profile');
      
      return response.data.data;
    } catch (error) {
      console.error('Profil getirilemedi:', error);
      throw error;
    }
  }

  // Çıkış
  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      console.error('Çıkış hatası:', error);
      // Çıkış hatası olsa bile local state'i temizle
    }
  }

  // ==================== DASHBOARD API METHODS ====================

  // Tüm dashboard verilerini getir
  async getDashboardData(): Promise<DashboardData> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        message: string;
        data: DashboardData;
      }> = await this.api.get('/dashboard');

      return response.data.data;
    } catch (error) {
      console.error('Dashboard verileri getirilemedi:', error);
      throw error;
    }
  }

  // Sadece dashboard metriklerini getir
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        message: string;
        data: DashboardMetrics;
      }> = await this.api.get('/dashboard/metrics');

      return response.data.data;
    } catch (error) {
      console.error('Dashboard metrikleri getirilemedi:', error);
      throw error;
    }
  }

  // Popüler ürünleri getir
  async getPopularProducts(limit?: number): Promise<PopularProduct[]> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        message: string;
        data: PopularProduct[];
      }> = await this.api.get(`/dashboard/popular-products${limit ? `?limit=${limit}` : ''}`);

      return response.data.data;
    } catch (error) {
      console.error('Popüler ürünler getirilemedi:', error);
      throw error;
    }
  }

  // Stok dışı ürünleri getir
  async getOutOfStockProducts(): Promise<OutOfStockProduct[]> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        message: string;
        data: OutOfStockProduct[];
      }> = await this.api.get('/dashboard/out-of-stock');

      return response.data.data;
    } catch (error) {
      console.error('Stok dışı ürünler getirilemedi:', error);
      throw error;
    }
  }

  // İşlemdeki siparişleri getir
  async getOrdersInProgress(): Promise<OrderSummary[]> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        message: string;
        data: OrderSummary[];
      }> = await this.api.get('/dashboard/orders-in-progress');

      return response.data.data;
    } catch (error) {
      console.error('İşlemdeki siparişler getirilemedi:', error);
      throw error;
    }
  }

  // Ödeme bekleyen siparişleri getir
  async getOrdersWaitingPayment(): Promise<OrderSummary[]> {
    try {
      const response: AxiosResponse<{
        success: boolean;
        message: string;
        data: OrderSummary[];
      }> = await this.api.get('/dashboard/orders-waiting-payment');

      return response.data.data;
    } catch (error) {
      console.error('Ödeme bekleyen siparişler getirilemedi:', error);
      throw error;
    }
  }
}

export const apiService = new ApiService();
