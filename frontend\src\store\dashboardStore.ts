// Dashboard Zustand Store
import { create } from 'zustand';
import { DashboardData, DashboardTab, DashboardUIState, OrderSummary } from '../types/dashboard.types';

interface DashboardStore {
  // Data state
  dashboardData: DashboardData | null;
  
  // UI state
  uiState: DashboardUIState;
  
  // Actions
  setDashboardData: (data: DashboardData) => void;
  setActiveTab: (tab: DashboardTab) => void;
  setSearchQuery: (query: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLastRefresh: (date: Date) => void;
  
  // Computed getters
  getFilteredOrders: () => OrderSummary[];
  getMetrics: () => DashboardData['metrics'] | null;
  getPopularProducts: () => DashboardData['popularProducts'] | [];
  getOutOfStockProducts: () => DashboardData['outOfStockProducts'] | [];
  
  // Reset
  reset: () => void;
}

const initialUIState: DashboardUIState = {
  activeTab: 'in-progress',
  searchQuery: '',
  isLoading: false,
  error: null,
  lastRefresh: null,
};

export const useDashboardStore = create<DashboardStore>((set, get) => ({
  // Initial state
  dashboardData: null,
  uiState: initialUIState,

  // Actions
  setDashboardData: (data: DashboardData) => {
    set({ 
      dashboardData: data,
      uiState: { 
        ...get().uiState, 
        lastRefresh: new Date(),
        error: null 
      }
    });
  },

  setActiveTab: (tab: DashboardTab) => {
    set({
      uiState: { 
        ...get().uiState, 
        activeTab: tab,
        searchQuery: '' // Tab değiştiğinde arama sıfırla
      }
    });
  },

  setSearchQuery: (query: string) => {
    set({
      uiState: { ...get().uiState, searchQuery: query }
    });
  },

  setLoading: (loading: boolean) => {
    set({
      uiState: { ...get().uiState, isLoading: loading }
    });
  },

  setError: (error: string | null) => {
    set({
      uiState: { ...get().uiState, error }
    });
  },

  setLastRefresh: (date: Date) => {
    set({
      uiState: { ...get().uiState, lastRefresh: date }
    });
  },

  // Computed getters
  getFilteredOrders: () => {
    const { dashboardData, uiState } = get();
    if (!dashboardData) return [];

    const { activeTab, searchQuery } = uiState;
    
    // Hangi sipariş listesini kullanacağımızı belirle
    const orders = activeTab === 'in-progress' 
      ? dashboardData.ordersInProgress 
      : dashboardData.ordersWaitingPayment;

    // Arama filtresi uygula
    if (!searchQuery.trim()) return orders;

    const query = searchQuery.toLowerCase().trim();
    return orders.filter(order => 
      order.orderNumber.toLowerCase().includes(query) ||
      order.customerName?.toLowerCase().includes(query) ||
      order.tableNumber?.toString().includes(query) ||
      order.items.some(item => 
        item.productName.toLowerCase().includes(query)
      )
    );
  },

  getMetrics: () => {
    const { dashboardData } = get();
    return dashboardData?.metrics || null;
  },

  getPopularProducts: () => {
    const { dashboardData } = get();
    return dashboardData?.popularProducts || [];
  },

  getOutOfStockProducts: () => {
    const { dashboardData } = get();
    return dashboardData?.outOfStockProducts || [];
  },

  // Reset
  reset: () => {
    set({
      dashboardData: null,
      uiState: initialUIState,
    });
  },
}));
