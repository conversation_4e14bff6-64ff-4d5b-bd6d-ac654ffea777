// Dashboard API için TypeScript tipleri
export type OrderStatus = 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'COMPLETED' | 'CANCELLED';
export type PaymentStatus = 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED';
export type OrderItemStatus = 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'SERVED';

// ==================== DASHBOARD METRICS ====================

export interface DashboardMetrics {
  // Günlük toplam kazanç
  dailyRevenue: {
    amount: number;
    changeFromYesterday: number; // Yüzde değişim
  };
  
  // İşlemdeki sipariş sayısı
  ordersInProgress: {
    count: number;
    changeFromYesterday: number;
  };
  
  // Ödeme bekleyen sipariş sayısı
  ordersWaitingPayment: {
    count: number;
    changeFromYesterday: number;
  };
  
  // Ek metrikler
  totalOrdersToday: number;
  averageOrderValue: number;
}

// ==================== POPULAR PRODUCTS ====================

export interface PopularProduct {
  id: string;
  name: string;
  categoryName: string;
  totalOrders: number;
  totalRevenue: number;
  image?: string;
}

// ==================== OUT OF STOCK PRODUCTS ====================

export interface OutOfStockProduct {
  id: string;
  name: string;
  categoryName: string;
  estimatedRestockTime?: string;
  image?: string;
}

// ==================== ORDER SUMMARY ====================

export interface OrderSummary {
  id: string;
  orderNumber: string;
  customerName?: string;
  tableNumber?: number;
  totalAmount: number;
  itemCount: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  orderedAt: Date;
  estimatedCompletionTime?: Date;
  
  // Sipariş kalemleri özeti
  items: OrderItemSummary[];
}

export interface OrderItemSummary {
  id: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  status: OrderItemStatus;
  notes?: string;
}

// ==================== MAIN DASHBOARD DATA ====================

export interface DashboardData {
  metrics: DashboardMetrics;
  popularProducts: PopularProduct[];
  outOfStockProducts: OutOfStockProduct[];
  ordersInProgress: OrderSummary[];
  ordersWaitingPayment: OrderSummary[];
  
  // Meta bilgiler
  lastUpdated: Date;
  branchId: string;
  branchName: string;
}

// ==================== HELPER TYPES ====================

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface MetricComparison {
  current: number;
  previous: number;
  changePercent: number;
}

// ==================== UI STATE TYPES ====================

export type DashboardTab = 'in-progress' | 'waiting-payment';

export interface DashboardUIState {
  activeTab: DashboardTab;
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
  lastRefresh: Date | null;
}

// ==================== API RESPONSE TYPES ====================

export interface DashboardApiResponse {
  success: boolean;
  message: string;
  data: DashboardData;
}

export interface DashboardMetricsApiResponse {
  success: boolean;
  message: string;
  data: DashboardMetrics;
}
